# =============================================================================
# UNIFIED MULTI-AGENT WEB SCRAPING SYSTEM - COMPREHENSIVE DEPENDENCIES
# =============================================================================
#
# This file contains all dependencies for the enhanced web scraping system
# including AI-powered agents, unified CLI interfaces, production web interface,
# enterprise security features, advanced data processing, and developer tools.
#
# System Features:
# - 🤖 20+ Specialized AI Agents (Intelligence, Security, Data Processing)
# - 🖥️ 4 Unified CLI Interfaces (Modern, Classic, Enhanced, Intelligent)
# - 🌐 Production Web Interface with Authentication & Real-time Monitoring
# - 🛡️ Enterprise Security & Compliance (GDPR, Anti-detection, Encryption)
# - 📊 Advanced Data Processing (NLP, Computer Vision, Data Enrichment)
# - 🔧 Developer Experience Tools (Visual Workflow Builder, Testing Framework)
# - 🏗️ Unified Integration System (Config, Auth, Data, Events)
# =============================================================================

# =============================================================================
# CORE WEB SCRAPING & HTTP
# =============================================================================

# HTTP clients and web scraping
requests>=2.31.0
httpx>=0.25.0
aiohttp>=3.9.0
requests-html>=0.10.0
urllib3>=2.0.7

# HTML/XML parsing
beautifulsoup4>=4.12.2
lxml>=4.9.3
parsel>=1.8.0
selectolax>=0.3.17

# Browser automation
selenium>=4.15.0
playwright>=1.40.0
undetected-chromedriver>=3.5.4

# Anti-detection and utilities
fake-useragent>=1.4.0
user-agents>=2.2.0
cloudscraper>=1.2.71

# =============================================================================
# WEB FRAMEWORK & API
# =============================================================================

# FastAPI and ASGI server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
gunicorn>=21.2.0
starlette>=0.27.0

# WebSocket support
websockets>=12.0
python-socketio>=5.10.0

# Template engine
jinja2>=3.1.2

# File upload handling
python-multipart>=0.0.6
aiofiles>=23.2.1

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# JWT and authentication (Enhanced for production web interface)
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0
oauthlib>=3.2.2
pyjwt>=2.8.0

# Additional security dependencies for enhanced web interface
python-multipart>=0.0.6  # For form data handling in authentication
email-validator>=2.1.0   # For email validation in user registration

# =============================================================================
# AI & LANGUAGE MODELS
# =============================================================================

# LangChain ecosystem
langchain>=0.1.0
langchain-core>=0.1.0
langchain-community>=0.0.20
langchain-openai>=0.0.5
langchain-anthropic>=0.1.0
langgraph>=0.0.30

# Pydantic for data validation
pydantic>=2.5.0
pydantic-settings>=2.1.0
pydantic-ai>=0.0.1

# AI providers
openai>=1.3.0
anthropic>=0.8.0
tiktoken>=0.5.0

# NLP and ML
transformers>=4.35.0
sentence-transformers>=2.2.2
nltk>=3.8.1
spacy>=3.7.0

# =============================================================================
# CLI & USER INTERFACE
# =============================================================================

# Rich CLI framework
rich>=13.7.0
rich-click>=1.7.0
textual>=0.45.0

# CLI utilities
typer>=0.9.0
click>=8.1.7
questionary>=2.0.0
prompt-toolkit>=3.0.41

# CLI enhancements
shellingham>=1.5.0
click-completion>=0.5.2
pyfiglet>=0.8.post1

# Progress bars and animations
tqdm>=4.66.0
alive-progress>=3.1.5

# =============================================================================
# DATA PROCESSING & STORAGE
# =============================================================================

# Data manipulation
pandas>=2.1.3
numpy>=1.25.0
pyarrow>=14.0.0
openpyxl>=3.1.2
xlsxwriter>=3.1.9

# Database
sqlalchemy>=2.0.23
alembic>=1.13.0
redis>=5.0.0

# Database drivers
asyncpg>=0.29.0  # PostgreSQL async
psycopg2-binary>=2.9.9  # PostgreSQL sync
pymongo>=4.6.0  # MongoDB

# =============================================================================
# DOCUMENT & FILE PROCESSING
# =============================================================================

# PDF processing
PyPDF2>=3.0.1
pdfplumber>=0.10.0
pymupdf>=1.23.0
pdf2image>=1.16.3

# Office documents
python-docx>=0.8.11
python-pptx>=0.6.23

# Image processing
Pillow>=10.1.0
opencv-python>=4.8.1
pytesseract>=0.3.10
scikit-image>=0.22.0

# =============================================================================
# CONFIGURATION & UTILITIES
# =============================================================================

# Configuration management
pyyaml>=6.0.1
python-dotenv>=1.0.0
configparser>=6.0.0

# Date and time
python-dateutil>=2.8.2
pytz>=2023.3
croniter>=1.4.1

# Async utilities
asyncio>=3.4.3
aiofiles>=23.2.1

# Type hints
typing-extensions>=4.8.0

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# System monitoring
psutil>=5.9.6
prometheus-client>=0.19.0

# Structured logging
structlog>=23.2.0
colorlog>=6.8.0

# =============================================================================
# TASK QUEUE & MESSAGING
# =============================================================================

# Task queue
celery>=5.3.0
kombu>=5.3.0

# Message brokers
redis>=5.0.0

# =============================================================================
# ENTERPRISE & DISTRIBUTED COMPUTING
# =============================================================================

# Distributed Redis
redis-py-cluster>=2.1.3

# Service discovery and configuration
consul-python>=1.1.0
etcd3>=0.12.0

# Container orchestration
kubernetes>=28.1.0
docker>=6.1.3

# Load balancing and clustering
haproxy-stats>=2.0.0

# Distributed coordination
zookeeper>=0.1.0

# Enterprise monitoring
statsd>=4.0.1

# =============================================================================
# TESTING & DEVELOPMENT
# =============================================================================

# Testing framework
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
pytest-mock>=3.12.0
pytest-xdist>=3.5.0

# Code quality
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.1
pre-commit>=3.6.0

# Development utilities
watchdog>=3.0.0
ipython>=8.17.0

# =============================================================================
# PHASE 6: DEVELOPER EXPERIENCE & USABILITY
# =============================================================================

# Visual Workflow Builder
fabric>=3.2.2
graphviz>=0.20.1
networkx>=3.2.1

# Plugin System
pluggy>=1.3.0
importlib-metadata>=6.8.0
stevedore>=5.1.0

# Advanced Testing Framework
pytest-html>=4.1.1
pytest-benchmark>=4.0.0
pytest-mock>=3.12.0
pytest-timeout>=2.2.0
pytest-rerunfailures>=12.1.2
locust>=2.17.0

# Mock Server
responses>=0.24.1
httpretty>=1.1.4
wiremock>=2.6.0

# Visual Components
pillow>=10.1.0
opencv-python>=********
selenium-wire>=5.1.0

# Workflow Serialization
jsonschema>=4.20.0
pydantic-yaml>=1.2.0

# =============================================================================
# UNIFIED SYSTEM INTEGRATION
# =============================================================================

# Event-driven architecture
asyncio-mqtt>=0.16.1
pydantic-settings>=2.1.0

# Configuration management
dynaconf>=3.2.4
python-decouple>=3.8

# Session management
itsdangerous>=2.1.2
cachetools>=5.3.2

# =============================================================================
# PHASE 5: ADVANCED DATA PROCESSING
# =============================================================================

# Enhanced NLP
spacy>=3.7.0
transformers>=4.35.0
sentence-transformers>=2.2.2
textblob>=0.17.1
langdetect>=1.0.9
googletrans>=4.0.0

# Computer Vision Enhancement
easyocr>=1.7.0
paddleocr>=2.7.0
torch>=2.1.0
torchvision>=0.16.0
ultralytics>=8.0.0

# Data Enrichment
geoip2>=4.7.0
phonenumbers>=8.13.0
email-validator>=2.1.0
validators>=0.22.0

# Image Processing
scikit-image>=0.22.0
imageio>=2.31.0
opencv-contrib-python>=4.8.1

# =============================================================================
# ENTERPRISE SECURITY & COMPLIANCE
# =============================================================================

# Advanced Anti-detection
selenium-stealth>=1.0.6
undetected-chromedriver>=3.5.4
fake-useragent>=1.4.0

# ML-based Fingerprinting
scikit-learn>=1.3.0
joblib>=1.3.0

# Encryption & Security
cryptography>=41.0.0
bcrypt>=4.1.0
argon2-cffi>=23.1.0

# GDPR Compliance
presidio-analyzer>=2.2.33
presidio-anonymizer>=2.2.33

# Audit Logging
structlog>=23.2.0
python-json-logger>=2.0.7

# =============================================================================
# INTELLIGENT CLI ENHANCEMENTS
# =============================================================================

# Modern CLI Framework
rich>=13.7.0
rich-click>=1.7.0
textual>=0.45.0
typer>=0.9.0

# Interactive Components
questionary>=2.0.0
prompt-toolkit>=3.0.41
click-completion>=0.5.2

# CLI Animations & Visuals
alive-progress>=3.1.5
pyfiglet>=0.8.post1
colorama>=0.4.6

# Natural Language Processing for CLI
fuzzywuzzy>=0.18.0
python-levenshtein>=0.23.0

# =============================================================================
# SCIENTIFIC & MACHINE LEARNING (OPTIONAL)
# =============================================================================

# Scientific computing
scipy>=1.11.0
scikit-learn>=1.3.0

# Visualization
matplotlib>=3.8.0
seaborn>=0.13.0
plotly>=5.17.0

# =============================================================================
# NETWORKING & PROXIES
# =============================================================================

# Proxy support
requests[socks]>=2.31.0
pysocks>=1.7.1

# Network utilities
dnspython>=2.4.2

# =============================================================================
# PLATFORM-SPECIFIC DEPENDENCIES
# =============================================================================

# Windows-specific
pywin32>=306; sys_platform == "win32"

# =============================================================================
# OPTIONAL CLOUD & EXTERNAL SERVICES
# =============================================================================

# Cloud storage (uncomment if needed)
# boto3>=1.34.0  # AWS S3
# google-cloud-storage>=2.10.0  # Google Cloud
# azure-storage-blob>=12.19.0  # Azure Blob

# Additional web drivers (uncomment if needed)
# chromedriver-autoinstaller>=0.6.2
# geckodriver-autoinstaller>=0.1.0

# =============================================================================
# REAL-TIME MONITORING & STREAMING
# =============================================================================

# WebSocket support
websockets>=12.0
python-socketio>=5.10.0
socketio>=5.10.0

# Real-time data streaming
redis-streams>=0.7.0
kafka-python>=2.0.2

# Server-Sent Events
sse-starlette>=1.8.2

# =============================================================================
# ENTERPRISE SCALABILITY
# =============================================================================

# Distributed computing
celery>=5.3.0
redis>=5.0.0
kombu>=5.3.0

# Load balancing
haproxy-stats>=2.0.0

# Service discovery
consul-python>=1.1.0
etcd3>=0.12.0

# Container orchestration
kubernetes>=28.1.0
docker>=6.1.3

# Distributed coordination
zookeeper>=0.1.0

# =============================================================================
# ADDITIONAL UTILITIES
# =============================================================================

# File format support
openpyxl>=3.1.2
xlsxwriter>=3.1.9
python-docx>=0.8.11
python-pptx>=0.6.23

# Date/time utilities
python-dateutil>=2.8.2
pytz>=2023.3
croniter>=1.4.1

# System utilities
psutil>=5.9.6
watchdog>=3.0.0

# Development tools
ipython>=8.17.0
jupyter>=1.0.0

# =============================================================================
# DEVELOPMENT & INSTALLATION NOTES
# =============================================================================

# REQUIRED POST-INSTALLATION COMMANDS:
# =====================================
#
# 1. Install language models:
#    python -m spacy download en_core_web_sm
#    python -m spacy download en_core_web_lg  # For advanced NLP
#
# 2. Install browser automation:
#    python -m playwright install
#    python -m playwright install-deps  # System dependencies
#
# 3. Download ML models (optional):
#    python -c "import transformers; transformers.pipeline('sentiment-analysis')"
#
# SYSTEM DEPENDENCIES:
# ===================
#
# For Tesseract OCR:
# - Ubuntu/Debian: sudo apt-get install tesseract-ocr tesseract-ocr-eng
# - macOS: brew install tesseract
# - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
#
# For Computer Vision (OpenCV):
# - Ubuntu/Debian: sudo apt-get install libgl1-mesa-glx libglib2.0-0
# - macOS: brew install opencv
# - Windows: Usually works out of the box
#
# For Audio Processing (optional):
# - Ubuntu/Debian: sudo apt-get install ffmpeg
# - macOS: brew install ffmpeg
# - Windows: Download from https://ffmpeg.org/
#
# OPTIONAL CLOUD DEPENDENCIES:
# ============================
# Uncomment these lines if you need cloud storage integration:
# boto3>=1.34.0  # AWS S3
# google-cloud-storage>=2.10.0  # Google Cloud
# azure-storage-blob>=12.19.0  # Azure Blob
#
# PERFORMANCE OPTIMIZATION:
# ========================
# For production deployments, consider installing:
# - uvloop>=0.19.0  # Faster event loop (Unix only)
# - orjson>=3.9.0  # Faster JSON parsing
# - cchardet>=2.1.7  # Faster character encoding detection
#
# SECURITY HARDENING:
# ==================
# For enhanced security in production:
# - python-jose[cryptography]>=3.3.0  # JWT with cryptography
# - passlib[argon2]>=1.7.4  # Secure password hashing
# - cryptography>=41.0.0  # Encryption utilities
